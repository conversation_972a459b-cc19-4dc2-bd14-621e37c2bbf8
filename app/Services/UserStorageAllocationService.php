<?php

namespace App\Services;

use App\Models\User;
use App\Models\OpenAiProject;
use App\Models\OpenAiProjectCapacity;
use App\Models\UserStorageAllocation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class UserStorageAllocationService
{
    /**
     * Allocate storage for a user based on their subscription tier.
     */
    public function allocateStorageForUser(User $user, string $subscriptionTier): UserStorageAllocation
    {
        return DB::transaction(function () use ($user, $subscriptionTier) {
            // Check if user already has an allocation
            $existingAllocation = UserStorageAllocation::where('user_id', $user->id)->first();
            
            if ($existingAllocation) {
                // Handle tier change
                return $this->updateUserAllocation($existingAllocation, $subscriptionTier);
            }

            // Find a project that can accommodate this user
            $project = $this->findAvailableProject($subscriptionTier);
            
            if (!$project) {
                // Create a new project if none available
                $project = $this->createNewProject();
            }

            $capacity = $project->capacity ?? $this->createCapacityRecord($project);
            
            // Allocate storage
            if (!$capacity->allocateUser($subscriptionTier)) {
                throw new Exception("Failed to allocate storage for user {$user->id}");
            }

            $guaranteedBytes = $capacity->getStorageForTier($subscriptionTier);

            $allocation = UserStorageAllocation::create([
                'user_id' => $user->id,
                'openai_project_id' => $project->id,
                'guaranteed_bytes' => $guaranteedBytes,
                'used_bytes' => 0,
                'subscription_tier' => $subscriptionTier,
                'allocated_at' => now(),
            ]);

            Log::info('Storage allocated for user', [
                'user_id' => $user->id,
                'project_id' => $project->id,
                'tier' => $subscriptionTier,
                'guaranteed_gb' => round($guaranteedBytes / (1024 * 1024 * 1024), 2)
            ]);

            return $allocation;
        });
    }

    /**
     * Update user allocation when subscription tier changes.
     */
    public function updateUserAllocation(UserStorageAllocation $allocation, string $newTier): UserStorageAllocation
    {
        return DB::transaction(function () use ($allocation, $newTier) {
            $oldTier = $allocation->subscription_tier;
            $capacity = $allocation->openaiProject->capacity;

            // Deallocate old tier
            $capacity->deallocateUser($oldTier);

            // Check if current project can accommodate new tier
            if ($capacity->canAccommodateUser($newTier)) {
                // Allocate new tier in same project
                $capacity->allocateUser($newTier);
                
                $newGuaranteedBytes = $capacity->getStorageForTier($newTier);
                
                $allocation->update([
                    'guaranteed_bytes' => $newGuaranteedBytes,
                    'subscription_tier' => $newTier,
                ]);

                // If downgrading and user is over new limit, they need to clean up
                if ($allocation->used_bytes > $newGuaranteedBytes) {
                    Log::warning('User over new storage limit after downgrade', [
                        'user_id' => $allocation->user_id,
                        'used_bytes' => $allocation->used_bytes,
                        'new_limit' => $newGuaranteedBytes
                    ]);
                }
            } else {
                // Need to move to different project
                $newProject = $this->findAvailableProject($newTier);
                
                if (!$newProject) {
                    $newProject = $this->createNewProject();
                }

                $newCapacity = $newProject->capacity ?? $this->createCapacityRecord($newProject);
                $newCapacity->allocateUser($newTier);

                $newGuaranteedBytes = $newCapacity->getStorageForTier($newTier);

                $allocation->update([
                    'openai_project_id' => $newProject->id,
                    'guaranteed_bytes' => $newGuaranteedBytes,
                    'subscription_tier' => $newTier,
                ]);

                // Update all user's cases to new project
                $allocation->user->caseFiles()->update(['openai_project_id' => $newProject->id]);
            }

            Log::info('User storage allocation updated', [
                'user_id' => $allocation->user_id,
                'old_tier' => $oldTier,
                'new_tier' => $newTier,
                'project_id' => $allocation->openai_project_id
            ]);

            return $allocation->fresh();
        });
    }

    /**
     * Find an available project that can accommodate a user tier.
     */
    private function findAvailableProject(string $tier): ?OpenAiProject
    {
        return OpenAiProject::where('is_active', true)
            ->whereHas('capacity', function ($query) use ($tier) {
                $query->where('accepts_new_users', true);
            })
            ->with('capacity')
            ->get()
            ->first(function ($project) use ($tier) {
                return $project->capacity->canAccommodateUser($tier);
            });
    }

    /**
     * Create a new OpenAI project when needed.
     */
    private function createNewProject(): OpenAiProject
    {
        // This would typically integrate with OpenAI API to create actual project
        // For now, create a placeholder that admin needs to configure
        
        $project = OpenAiProject::create([
            'name' => 'Auto-created Project ' . now()->format('Y-m-d H:i'),
            'api_key' => 'NEEDS_CONFIGURATION',
            'organization_id' => null,
            'storage_used' => 0,
            'is_active' => false, // Inactive until admin configures
        ]);

        $this->createCapacityRecord($project);

        Log::warning('New OpenAI project created - requires admin configuration', [
            'project_id' => $project->id,
            'project_name' => $project->name
        ]);

        return $project;
    }

    /**
     * Create capacity tracking record for a project.
     */
    private function createCapacityRecord(OpenAiProject $project): OpenAiProjectCapacity
    {
        return OpenAiProjectCapacity::create([
            'openai_project_id' => $project->id,
            'total_capacity_bytes' => 100 * 1024 * 1024 * 1024, // 100GB
            'allocated_bytes' => 0,
            'used_bytes' => 0,
            'reserved_bytes' => 5 * 1024 * 1024 * 1024, // 5GB buffer
            'basic_users' => 0,
            'standard_users' => 0,
            'pro_users' => 0,
            'accepts_new_users' => true,
        ]);
    }

    /**
     * Get user's storage information.
     */
    public function getUserStorageInfo(User $user): ?array
    {
        $allocation = UserStorageAllocation::where('user_id', $user->id)->first();
        
        if (!$allocation) {
            return null;
        }

        return $allocation->getStorageInfo();
    }

    /**
     * Check if user can upload a file.
     */
    public function canUserUploadFile(User $user, int $fileSize): bool
    {
        $allocation = UserStorageAllocation::where('user_id', $user->id)->first();
        
        if (!$allocation) {
            return false;
        }

        return $allocation->canAccommodateFile($fileSize);
    }

    /**
     * Update user's storage usage when documents are added/removed.
     */
    public function updateUserStorageUsage(User $user, int $bytesChange): void
    {
        $allocation = UserStorageAllocation::where('user_id', $user->id)->first();
        
        if ($allocation) {
            $newUsage = max(0, $allocation->used_bytes + $bytesChange);
            $allocation->update(['used_bytes' => $newUsage]);

            // Also update project capacity tracking
            $capacity = $allocation->openaiProject->capacity;
            if ($capacity) {
                $newProjectUsage = max(0, $capacity->used_bytes + $bytesChange);
                $capacity->update(['used_bytes' => $newProjectUsage]);
            }
        }
    }
}
