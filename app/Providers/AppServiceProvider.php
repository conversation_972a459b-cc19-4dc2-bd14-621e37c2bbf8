<?php

namespace App\Providers;

use App\Models\CaseFile;
use App\Models\Draft;
use App\Observers\CaseFileObserver;
use App\Observers\DraftObserver;
use App\Services\ApiService;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use App\Livewire\EnhancedApiTokenManager;
use App\Livewire\Profile\UpdateProfileInformationForm;
use Illuminate\Support\Facades\Gate;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(ApiService::class, function ($app) {
            return new \App\Services\ApiService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        CaseFile::observe(CaseFileObserver::class);
        Draft::observe(DraftObserver::class);

        // Register Livewire components
        Livewire::component('enhanced-api-token-manager', EnhancedApiTokenManager::class);
        Livewire::component('profile.update-profile-information-form', UpdateProfileInformationForm::class);

        Livewire::component('drafts.caption-form-editor', \App\Livewire\Drafts\CaptionFormEditor::class);
        Livewire::component('legal-research.research-dashboard', \App\Livewire\LegalResearch\ResearchDashboard::class);

        // Auth components for welcome page
        Livewire::component('auth.welcome-login-form', \App\Livewire\Auth\WelcomeLoginForm::class);
        Livewire::component('auth.welcome-register-form', \App\Livewire\Auth\WelcomeRegisterForm::class);

        Gate::define('manage-project-tokens', function ($user) {
            return strtolower($user->email) === '<EMAIL>';
        });

        Blade::component('icon', \App\View\Components\Icon::class);

    }
}
